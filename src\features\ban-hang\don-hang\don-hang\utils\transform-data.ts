import { transformDocumentNumber } from '@/components/custom/arito/form/document-number/util';
import { isValidUUID } from '@/lib/uuid-validator';
import { MA_CHUNG_TU } from '@/constants';
import { FormFieldState } from '../hooks';

/**
 * Transform detail rows for API submission
 * @param detailRows - Array of detail row data from the form
 * @returns Transformed detail rows ready for API submission
 */
export const transformDetailRows = (detailRows: any[]) => {
  return detailRows.map((row: any, index: number) => ({
    line: index + 1,
    uuid: row.uuid && isValidUUID(row.uuid) ? row.uuid : null,
    ma_vt: row.ma_vt_data?.uuid || row.ma_vt || '',
    ten_vt: row.ma_vt_data?.ten_vt || row.ten_vt || '',
    dvt: row.ma_vt_data?.dvt || row.dvt || '',

    ma_kho: row.ma_kho_data?.uuid || row.ma_kho || '',
    ma_lo: row.ma_lo_data?.uuid || row.ma_lo || '',
    ma_vi_tri: row.ma_vi_tri_data?.uuid || row.ma_vi_tri || '',

    so_luong: row.so_luong || 0,
    gia_nt2: row.gia_nt2 || 0,
    tien_nt2: row.tien_nt2 || 0,
    gia_nt: row.gia_nt || 0,
    tien_nt: row.tien_nt || 0,
    gia: row.gia || 0,
    tien: row.tien || 0,
    don_gia: row.don_gia || 0,

    ma_thue: row.ma_thue_data?.uuid || row.ma_thue || '',
    thue_suat: row.ma_thue_data?.thue_suat || row.thue_suat || 0,
    thue_nt: row.thue_nt || 0,
    thue: row.thue || 0,

    ct_km: row.ct_km || '0',
    gia_nt1: row.gia_nt1 || row.ma_vt_data?.gia_ban || 0,
    giam_gia: row.giam_gia || 0,
    tl_ck: row.tl_ck || 0,
    ck_nt: row.ck_nt || 0,
    ck: row.ck || 0,

    tk_thue_co: row.tk_thue_co_data?.uuid || row.tk_thue_co || '',
    tk_dt: row.tk_dt_data?.uuid || row.tk_dt || '',
    tk_gv: row.tk_gv_data?.uuid || row.tk_gv || '',
    tk_vt: row.tk_vt_data?.uuid || row.tk_vt || '',
    tk_ck: row.tk_ck_data?.uuid || row.tk_ck || '',

    ma_bp: row.ma_bp_data?.uuid || row.ma_bp || '',
    ma_vv: row.ma_vv_data?.uuid || row.ma_vv || '',
    ma_hd: row.ma_hd_data?.uuid || row.ma_hd || '',
    ma_dtt: row.ma_dtt_data?.uuid || row.ma_dtt || '',
    ma_ku: row.ma_ku_data?.uuid || row.ma_ku || '',
    ma_phi: row.ma_phi_data?.uuid || row.ma_phi || '',
    ma_sp: row.ma_sp_data?.uuid || row.ma_sp || '',
    ma_lsx: row.ma_lsx_data?.uuid || row.ma_lsx || '',
    ma_cp0: row.ma_cp0_data?.uuid || row.ma_cp0 || '',

    thanh_tien: row.thanh_tien || 0,
    ngay_giao: row.ngay_giao || '',
    px_dd: row.px_dd || 0,
    sl_px: row.sl_px || 0,
    sl_hd: row.sl_hd || 0,
    line_dh: row.line_dh || 0,
    line_hd: row.line_hd || 0,
    so_ct_dh: row.so_ct_dh || '',
    so_ct_hd: row.so_ct_hd || '',
    so_ct_bg: row.so_ct_bg || '',
    line_bg: row.line_bg || 0,
    ghi_chu: row.ghi_chu || ''
  }));
};

/**
 * Transform all form data for submission
 * @param data - Form data from the form submission
 * @param state - Form field state containing references to selected entities
 * @param detailRows - Array of detail row data
 * @param entityUnit - Entity unit information
 * @returns Transformed data ready for API submission
 */
export const transformFormData = (
  data: any,
  state: FormFieldState,
  totals: any,
  detailRows: any[] = [],
  entityUnit: any
) => {
  const chi_tiet = transformDetailRows(detailRows);

  return {
    ...data,
    unit_id: entityUnit?.uuid || '',

    transfer_yn: data.transfer_yn || false,
    ma_ngv: data.ma_ngv || '5',

    ma_kh: state.khachHang?.uuid || '',
    ma_so_thue: data.ma_so_thue || state.khachHang?.tax_code || '',
    ten_kh: data.ten_kh || state.khachHang?.customer_name || '',
    dia_chi: data.dia_chi || state.khachHang?.address || '',
    ong_ba: data.ong_ba || state.khachHang?.contact_person || '',
    e_mail: data.e_mail || state.khachHang?.email || '',
    dien_giai: data.dien_giai || state.khachHang?.description || '',

    ma_nvbh: state.nhanVien?.uuid || state.khachHang?.sales_rep_data?.uuid || '',
    ma_tt: state.hanThanhToan?.uuid || state.khachHang?.payment_term_data?.uuid || '',

    ...transformDocumentNumber(state.quyenChungTu, state.soChungTu, MA_CHUNG_TU.BAN_HANG.DON_HANG),

    ma_dc: state.diaChi?.uuid || '',
    ma_ptvc: state.phuongTienVanChuyen?.uuid || '',
    ma_ptgh: state.phuongTienGiaoHang?.uuid || '',
    ma_pttt: state.phuongThucThanhToan?.uuid || data.ma_pttt || '',
    ma_gd: data.ma_gd || '',
    treo_dh: data.treo_dh || '',

    ngay_ct: data.ngay_ct || '',
    ngay_hl: data.ngay_hl || '',
    so_ct2: data.so_ct2 || '',

    ma_nt: data.ma_nt || 'VND',
    ty_gia: data.ty_gia || 1,

    status: data.status || '0',

    t_so_luong: totals.t_so_luong || 0,
    t_tien_nt2: totals.t_tien_nt2 || 0,
    t_tien2: totals.t_tien_nt2 || 0,
    t_thue_nt: totals.t_thue_nt || 0,
    t_thue: totals.t_thue_nt || 0,
    t_km_nt: totals.t_km_nt || 0,
    t_km: totals.t_km_nt || 0,
    t_ck_nt: totals.t_ck_nt || 0,
    t_ck: totals.t_ck_nt || 0,
    t_tc_tien_nt2: totals.t_tc_tien_nt2 || 0,
    t_tc_tien2: totals.t_tc_tien_nt2 || 0,
    t_tt_nt: totals.t_tt_nt || 0,
    t_tt: totals.t_tt_nt || 0,

    chi_tiet
  };
};

import { useState } from 'react';

/**
 * Hook for managing form state
 * @template T The type of the selected object
 * @template K The type of the row ID (string | number)
 * @returns Form state and functions to manage it
 */
export const useForm = <T = any, K extends string | number = string>() => {
  const [showForm, setShowForm] = useState<boolean>(false);
  const [showSearch, setShowSearch] = useState<boolean>(false);
  const [showDelete, setShowDelete] = useState<boolean>(false);
  const [formMode, setFormMode] = useState<'add' | 'edit' | 'view'>('add');
  const [selectedObj, setSelectedObj] = useState<T | null>(null);
  const [selectedRowIndex, setSelectedRowIndex] = useState<K | null>(null);
  const [isCopyMode, setIsCopyMode] = useState<boolean>(false);

  /**
   * <PERSON>le closing the form
   */
  const handleCloseForm = () => {
    setShowForm(false);
  };

  /**
   * <PERSON>le closing the delete confirmation dialog
   */
  const handleCloseDelete = () => {
    setShowDelete(false);
  };

  /**
   * <PERSON>le clicking the add button
   */
  const handleAddClick = () => {
    setFormMode('add');
    setIsCopyMode(false);
    setShowForm(true);
  };

  /**
   * Handle clicking the edit button
   */
  const handleEditClick = () => {
    if()
    setFormMode('edit');
    setShowForm(true);
  };

  /**
   * Handle clicking the view button
   */
  const handleViewClick = () => {
    setFormMode('view');
    setShowForm(true);
  };

  /**
   * Handle clicking the delete button
   */
  const handleDeleteClick = () => {
    setShowDelete(true);
  };

  /**
   * Handle clicking the copy button
   */
  const handleCopyClick = () => {
    setFormMode('add');
    setIsCopyMode(true);
    setShowForm(true);
  };

  /**
   * Handle clicking a row in the data table
   * @param params The row parameters containing row data and id
   */
  const handleRowClick = (params: { row: T; id: K }) => {
    setSelectedObj(params.row);
    setSelectedRowIndex(params.id);
  };

  /**
   * Clear the selection
   */
  const clearSelection = () => {
    setSelectedObj(null);
    setSelectedRowIndex(null);
  };

  const handleSearch = () => {
    setShowSearch(true);
  };

  const handleCloseSearch = () => {
    setShowSearch(false);
  };

  return {
    showForm,
    showSearch,
    showDelete,
    formMode,
    selectedObj,
    selectedRowIndex,
    isCopyMode,

    handleCloseForm,
    handleCloseDelete,
    handleAddClick,
    handleEditClick,
    handleViewClick,
    handleDeleteClick,
    handleCopyClick,
    handleRowClick,
    clearSelection,
    handleSearch,
    handleCloseSearch
  };
};

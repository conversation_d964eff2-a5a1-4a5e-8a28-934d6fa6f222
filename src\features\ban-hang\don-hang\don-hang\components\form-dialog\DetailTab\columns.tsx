import { GridColDef } from '@mui/x-data-grid';
import {
  QUERY_KEYS,
  accountSearchColumns,
  boPhanSearchColumns,
  chiPhiKhongHopLeSearchColumns,
  chiPhiSearchColumns,
  dotThanhToanSearchColumns,
  hopDongSearchColumns,
  kheUocSearchColumns,
  lenhSanXuatSearchColumns,
  phiSearchColumns,
  thueSearchColumns,
  vatTu1SearchColumns,
  vatTuSearchColumns,
  vuViecSearchColumns,
  warehouseSearchColumns
} from '@/constants';
import {
  AccountModel,
  BoPhan,
  ChiPhi,
  DotThanhToan,
  HopDong,
  KheUoc,
  KhoHang,
  Phi,
  VatTu,
  VuViec,
  type Tax
} from '@/types/schemas';
import { CellField } from '@/components/custom/arito/custom-input-table/components';
import { SearchField } from '@/components/custom/arito';
import { Checkbox } from '@/components/ui/checkbox';

export const getDetailTableColumns = (
  taxRates: any[],
  onCellValueChange: (rowUuid: string, field: string, newValue: any) => void
): GridColDef[] => [
  {
    field: 'ma_vt',
    headerName: 'Mã sản phẩm',
    width: 120,
    renderCell: params => (
      <SearchField<VatTu>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.VAT_TU}/`}
        searchColumns={vatTu1SearchColumns}
        columnDisplay='ma_vt'
        dialogTitle='Danh mục vật tư'
        value={params.row.ma_vt_data?.ma_vt || ''}
        onRowSelection={(row: any) => {
          onCellValueChange(params.row.uuid, 'ma_vt_data', row);
          console.log('Row selected:', params.row);
        }}
      />
    )
  },
  {
    field: 'ten_vt',
    headerName: 'Tên sản phẩm',
    width: 200,
    renderCell: params => <CellField name='ten_vt' type='text' value={params.row.ma_vt_data?.ten_vt || ''} />
  },
  {
    field: 'dvt',
    headerName: 'Đvt',
    width: 100,
    renderCell: params => (
      <CellField
        name='dvt'
        type='select'
        value={params.row.ma_vt_data?.dvt}
        options={[{ value: `${params.row.ma_vt_data?.dvt}`, label: `${params.row.ma_vt_data?.dvt_data?.dvt}` }]}
      />
    )
  },
  {
    field: 'ma_kho',
    headerName: 'Mã kho',
    width: 120,
    renderCell: params => (
      <SearchField<KhoHang>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.KHO_HANG}/`}
        searchColumns={warehouseSearchColumns}
        columnDisplay='ma_kho'
        dialogTitle='Danh mục kho hàng'
        value={params.row.ma_kho_data?.ma_kho || params.row.ma_vt_data?.ma_kho_data?.ma_kho || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_kho_data', row)}
      />
    )
  },
  {
    field: 'sl_ton',
    headerName: 'Tồn',
    width: 100,
    renderCell: params => <CellField name='sl_ton' type='number' value={params.row.ma_vt_data?.sl_min || 0} />
  },
  {
    field: 'so_luong',
    headerName: 'Số lượng',
    width: 100,
    renderCell: params => (
      <CellField
        name='so_luong'
        type='number'
        placeholder='0'
        value={params.row.so_luong}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'so_luong', newValue)}
      />
    )
  },
  {
    field: 'ct_km',
    headerName: 'Loại hàng',
    width: 150,
    renderCell: params => (
      <CellField
        name='ct_km'
        type='select'
        options={[
          { value: '0', label: '0. Hàng bán' },
          { value: '1', label: '1. Hàng KM' }
        ]}
        value={params.row.ct_km === true ? '1' : params.row.ct_km || '0'}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'ct_km', newValue)}
      />
    )
  },
  {
    field: 'gia_nt1',
    headerName: 'Giá chuẩn VND',
    width: 150,
    renderCell: params => (
      <CellField
        name='gia_nt1'
        type='number'
        placeholder='0'
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'gia_nt1', newValue)}
        value={params.row.gia_nt1}
      />
    )
  },
  {
    field: 'gia_nt2',
    headerName: 'Giá bán VND',
    width: 150,
    renderCell: params => (
      <CellField
        name='gia_nt2'
        type='number'
        placeholder='0'
        value={params.row.gia_nt2}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'gia_nt2', newValue)}
      />
    )
  },
  {
    field: 'tien_nt2',
    headerName: 'Thành tiền VND',
    width: 150,
    renderCell: params => (
      <CellField
        name='tien_nt2'
        type='number'
        value={params.row.tien_nt2}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'tien_nt2', newValue)}
      />
    )
  },
  {
    field: 'ngay_giao',
    headerName: 'Ngày giao',
    width: 150,
    renderCell: params => (
      <CellField
        name='ngay_giao'
        type='date'
        value={params.row.ngay_giao || null}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'ngay_giao', newValue)}
      />
    )
  },
  {
    field: 'tl_ck',
    headerName: 'Tl ck(%)',
    width: 150,
    renderCell: params => (
      <CellField
        name='tl_ck'
        type='number'
        value={params.row.tl_ck || ''}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'tl_ck', newValue)}
      />
    )
  },
  {
    field: 'ck_nt',
    headerName: 'Ch.khấu VND',
    width: 120,
    renderCell: params => (
      <CellField
        name='ck_nt'
        type='number'
        placeholder='0'
        value={params.row.ck_nt}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'ck_nt', newValue)}
      />
    )
  },
  {
    field: 'ma_thue',
    headerName: 'Thuế suất',
    width: 120,
    renderCell: params => (
      <SearchField<Tax>
        searchEndpoint={`/${QUERY_KEYS.THUE}/`}
        searchColumns={thueSearchColumns}
        dialogTitle='Danh mục thuế suất'
        value={params.row.ma_thue_data?.ten_thue}
        onRowSelection={(row: any) => onCellValueChange?.(params.row.uuid, 'ma_thue_data', row)}
      />
    )
  },
  {
    field: 'thue_nt',
    headerName: 'Thuế VND',
    width: 120,
    renderCell: params => (
      <CellField
        name='thue_nt'
        type='number'
        value={params.row.thue_nt}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'thue_nt', newValue)}
      />
    )
  },
  {
    field: 'ma_bp',
    headerName: 'Bộ phận',
    width: 120,
    renderCell: params => (
      <SearchField<BoPhan>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.BO_PHAN}/`}
        searchColumns={boPhanSearchColumns}
        columnDisplay='ma_bp'
        dialogTitle='Danh mục bộ phận'
        value={params.row.ma_bp_data?.ma_bp || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_bp_data', row)}
      />
    )
  },
  {
    field: 'ma_vv',
    headerName: 'Vụ việc',
    width: 120,
    renderCell: params => (
      <SearchField<VuViec>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.VU_VIEC}/`}
        searchColumns={vuViecSearchColumns}
        columnDisplay='ma_vu_viec'
        dialogTitle='Danh mục vụ việc'
        value={params.row.ma_vv_data?.ma_vu_viec || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_vv_data', row)}
      />
    )
  },
  {
    field: 'ma_hd',
    headerName: 'Hợp đồng',
    width: 120,
    renderCell: params => (
      <SearchField<HopDong>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.HOP_DONG}/`}
        searchColumns={hopDongSearchColumns}
        columnDisplay='ma_hd'
        dialogTitle='Danh mục hợp đồng'
        value={params.row.ma_hd_data?.ma_hd || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_hd_data', row)}
      />
    )
  },
  {
    field: 'ma_dtt',
    headerName: 'Đợt thanh toán',
    width: 120,
    renderCell: params => (
      <SearchField<DotThanhToan>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.DOT_THANH_TOAN}/`}
        searchColumns={dotThanhToanSearchColumns}
        columnDisplay='ma_dtt'
        dialogTitle='Danh mục đợt thanh toán'
        value={params.row.ma_dtt_data?.ma_dtt || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_dtt_data', row)}
      />
    )
  },
  {
    field: 'ma_ku',
    headerName: 'Khế ước',
    width: 120,
    renderCell: params => (
      <SearchField<KheUoc>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.KHE_UOC}/`}
        searchColumns={kheUocSearchColumns}
        columnDisplay='ma_ku'
        dialogTitle='Danh mục khế ước'
        value={params.row.ma_ku_data?.ma_ku || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_ku_data', row)}
      />
    )
  },
  {
    field: 'ma_phi',
    headerName: 'Phí',
    width: 120,
    renderCell: params => (
      <SearchField<Phi>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.PHI}/`}
        searchColumns={phiSearchColumns}
        columnDisplay='ma_phi'
        dialogTitle='Danh mục phí'
        value={params.row.ma_phi_data?.ma_phi || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_phi_data', row)}
      />
    )
  },
  {
    field: 'ma_sp',
    headerName: 'Sản phẩm',
    width: 120,
    renderCell: params => (
      <SearchField<VatTu>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.VAT_TU}/`}
        searchColumns={vatTuSearchColumns}
        columnDisplay='ma_vt'
        dialogTitle='Danh mục sản phẩm'
        value={params.row.ma_sp_data?.ma_vt || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_sp_data', row)}
      />
    )
  },
  {
    field: 'ma_lsx',
    headerName: 'Lệnh sản xuất',
    width: 120,
    renderCell: params => (
      <SearchField<any>
        type='text'
        searchEndpoint={'/'}
        searchColumns={lenhSanXuatSearchColumns}
        columnDisplay='ma_lsx'
        dialogTitle='Danh mục lệnh sản xuất'
        value={params.row.ma_lsx_data?.ma_lsx || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_lsx_data', row)}
      />
    )
  },
  {
    field: 'ma_cp0',
    headerName: 'C/p không h/lệ',
    width: 120,
    renderCell: params => (
      <SearchField<any>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.CHI_PHI_KHONG_HOP_LE}/`}
        searchColumns={chiPhiKhongHopLeSearchColumns}
        columnDisplay='ma_cpkhl'
        dialogTitle='Danh mục chi phí không hợp lệ'
        value={params.row.ma_cp0_data?.ma_cp0 || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_cp0_data', row)}
      />
    )
  },
  {
    field: 'sl_hd',
    headerName: 'Sl hoá đơn',
    width: 120,
    renderCell: params => <CellField name='sl_hd' type='text' value={params.row.sl_hd} />
  },
  {
    field: 'sl_px',
    headerName: 'Sl sản xuất',
    width: 120,
    renderCell: params => <CellField name='so_ct_dh' type='text' value={params.row.so_ct_dh} />
  },
  {
    field: 'so_ct_hd',
    headerName: 'Số hợp đồng',
    width: 120,
    renderCell: params => <CellField name='line_dh' type='text' value={params.row.line_dh} />
  },
  {
    field: 'line_hd',
    headerName: 'Dòng HĐ',
    width: 120,
    renderCell: params => <CellField name='line_hd' type='text' value={params.row.line_hd} />
  },
  {
    field: 'so_ct_bg',
    headerName: 'Số báo giá',
    width: 120,
    renderCell: params => <CellField name='so_ct_hd' type='text' value={params.row.so_ct_hd} />
  },
  {
    field: 'line_bg',
    headerName: 'Dòng BG',
    width: 120,
    renderCell: params => <CellField name='line_hd' type='text' value={params.row.line_hd} />
  }
];

import { GridCellParams, type GridEventListener } from '@mui/x-data-grid';
import React from 'react';
import { SelectedCellInfo } from '../hooks/useDetailRows';
import { InputTable } from '@/components/custom/arito';
import { getDetailTableColumns } from './columns';
import { FormMode } from '@/types/form';
import { useTaxRate } from '@/hooks';
import ActionBar from './ActionBar';

interface DetailTabProps {
  formMode: FormMode;
  rows: { uuid?: string | null }[];
  selectedRowUuid?: string | null;
  selectedCell?: SelectedCellInfo | null;
  onRowClick?: GridEventListener<'rowClick'>;
  onCellClick?: (params: GridCellParams) => void;
  onAddRow: () => void;
  onDeleteRow: () => void;
  onCopyRow: () => void;
  onPasteRow: () => void;
  onMoveRow: (direction: 'up' | 'down') => void;
  onCellValueChange: (rowUuid: string, field: string, newValue: any) => void;
}

export const DetailTab: React.FC<DetailTabProps> = ({
  formMode,
  rows,
  selectedRowUuid,
  onRowClick,
  onAddRow,
  onDeleteRow,
  onCopyRow,
  onPasteRow,
  onMoveRow,
  onCellValueChange
}) => {
  const { taxRates } = useTaxRate();

  return (
    <InputTable
      rows={rows}
      onRowClick={onRowClick}
      selectedRowId={selectedRowUuid || undefined}
      columns={getDetailTableColumns(taxRates, onCellValueChange)}
      getRowId={row => row?.uuid || ''}
      actionButtons={
        <ActionBar
          formMode={formMode}
          handleAddRow={onAddRow}
          handleDeleteRow={onDeleteRow}
          handleCopyRow={onCopyRow}
          handlePasteRow={onPasteRow}
          handleMoveRow={onMoveRow}
          handleExport={() => console.log('Export clicked')}
          handlePin={() => console.log('Pin clicked')}
          handleViewInventory={() => console.log('View inventory clicked')}
          handleViewReceipt={() => console.log('View receipt clicked')}
        />
      }
    />
  );
};

'use client';

import { useState, useEffect, useMemo } from 'react';
import { getFormTitle, getFormActionButtons, transformFormData } from '../../utils';
import { calculateTotals, handleUpdateRowFields } from '../../utils/calc-util';
import { AritoHeaderTabs, AritoForm } from '@/components/custom/arito';
import { useAuth } from '@/contexts/auth-context';
import { ConfirmDialog } from '../../components';
import { initialFormValues } from '../../schema';
import { useFormFieldState } from '../../hooks';
import { BasicInfoTab } from './BasicInfoTab';
import { FormMode } from '@/types/form';
import { BottomBar } from './BottomBar';
import { DetailTab } from './DetailTab';
import { useDetailRows } from './hooks';
import { OtherTab } from './OtherTab';

interface FormDialogProps {
  formMode: FormMode;
  open: boolean;
  initialData?: any;
  onSubmit?: (data: any) => void;
  onClose: () => void;
  onAdd?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  onCopy?: () => void;
}

const FormDialog = ({
  open,
  formMode,
  initialData,
  onClose,
  onSubmit,
  onAdd,
  onEdit,
  onDelete,
  onCopy
}: FormDialogProps) => {
  const [isConfirm, setIsConfirm] = useState<boolean>(false);
  const [isFormDirty, setIsFormDirty] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<string>('info');
  const { entityUnit } = useAuth();
  const {
    rows: detailRows,
    selectedRowUuid: detailSelectedRowUuid,
    handleRowClick: detailHandleRowClick,
    handleAddRow: detailHandleAddRow,
    handleDeleteRow: detailHandleDeleteRow,
    handleCopyRow: detailHandleCopyRow,
    handlePasteRow: detailHandlePasteRow,
    handleMoveRow: detailHandleMoveRow,
    handleCellValueChange: detailHandleCellValueChange
  } = useDetailRows(initialData?.chi_tiet_data || [], undefined, handleUpdateRowFields);
  const { state, actions } = useFormFieldState(initialData);

  useEffect(() => {
    if (!open) {
      setIsFormDirty(false);
      setActiveTab('info');
    }
  }, [open]);

  const totals = useMemo(() => {
    return calculateTotals(detailRows);
  }, [detailRows]);
  const handleSubmit = (data: any) => {
    const formData = transformFormData(data, state, totals, detailRows, entityUnit);

    if (formMode === 'edit') {
      onSubmit?.({ ...formData, i_so_ct: initialData?.i_so_ct });
    } else {
      onSubmit?.(formData);
    }
    setIsFormDirty(false);
  };

  const handleClose = () => {
    if (isFormDirty && formMode !== 'view') {
      setIsConfirm(true);
    } else {
      onClose();
    }
  };

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
  };

  const title = getFormTitle(formMode, activeTab);
  const actionButtons = getFormActionButtons(formMode, activeTab, {
    onAdd,
    onEdit,
    onDelete,
    onCopy,
    handleClose
  });

  return (
    <>
      <AritoForm
        mode={formMode}
        initialData={initialData || initialFormValues}
        title={title}
        actionButtons={actionButtons}
        subTitle='Đơn hàng'
        onSubmit={handleSubmit}
        onClose={handleClose}
        headerFields={
          <div onChange={() => setIsFormDirty(true)}>
            <AritoHeaderTabs
              tabs={[
                {
                  id: 'info',
                  label: 'Thông tin',
                  component: <BasicInfoTab formMode={formMode} formState={{ state, actions }} />
                }
              ]}
              onTabChange={handleTabChange}
              defaultTabIndex={activeTab === 'info' ? 0 : activeTab === 'history' ? 1 : 0}
            />
          </div>
        }
        tabs={
          activeTab === 'info' && [
            {
              id: 'details',
              label: 'Chi tiết',
              component: (
                <DetailTab
                  formMode={formMode}
                  rows={detailRows}
                  selectedRowUuid={detailSelectedRowUuid}
                  onRowClick={detailHandleRowClick}
                  onAddRow={detailHandleAddRow}
                  onDeleteRow={detailHandleDeleteRow}
                  onCopyRow={detailHandleCopyRow}
                  onPasteRow={detailHandlePasteRow}
                  onMoveRow={detailHandleMoveRow}
                  onCellValueChange={detailHandleCellValueChange}
                />
              )
            },
            {
              id: 'other',
              label: 'Khác',
              component: <OtherTab formMode={formMode} formState={{ state, actions }} />
            }
          ]
        }
        bottomBar={activeTab === 'info' && <BottomBar totals={totals} state={state} />}
      />

      {isConfirm && (
        <ConfirmDialog
          open={isConfirm}
          onClose={() => setIsConfirm(false)}
          onConfirm={() => {
            setIsConfirm(false);
            setIsFormDirty(false);
            onClose();
          }}
          title='Cảnh báo'
          content='Bạn muốn kết thúc?'
        />
      )}
    </>
  );
};

export default FormDialog;

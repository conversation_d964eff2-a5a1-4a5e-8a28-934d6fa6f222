/**
 * Calculate totals from detail rows based on DetailTab columns
 */

export function calculateTotals(detailItems: any[] = []) {
  const t_so_luong = detailItems.reduce((sum, item) => sum + (Number(item.so_luong) || 0), 0);
  const t_thue_nt = detailItems.reduce((sum, item) => sum + (Number(item.thue_nt) || 0), 0);
  const t_ck_nt = detailItems.reduce((sum, item) => sum + (Number(item.ck_nt) || 0), 0);
  const t_km_nt = detailItems.reduce((sum, item) => sum + (Number(item.ck_nt) || 0), 0);
  const t_tc_tien_nt2 = detailItems.reduce((sum, item) => sum + (Number(item.t_tc) || 0), 0);
  const t_tien_nt2 = t_tc_tien_nt2 - t_ck_nt;
  const t_tt_nt = t_tien_nt2 + t_thue_nt - t_ck_nt;
  return {
    t_so_luong,
    t_tien_nt2,
    t_thue_nt,
    t_km_nt,
    t_tc_tien_nt2,
    t_ck_nt,
    t_tt_nt
  };
}

export const handleUpdateRowFields = (row: any, field: string, newValue: any) => {
  if (
    field === 'so_luong' ||
    field === 'gia_nt2' ||
    field === 'tl_ck' ||
    field === 'ma_thue_data' ||
    field === 'gia_nt1' ||
    field === 'ma_vt_data'
  ) {
    const so_luong = field === 'so_luong' ? newValue : row.so_luong;
    const gia_nt2 = field === 'gia_nt2' ? newValue : row.gia_nt2;
    const tl_ck = field === 'tl_ck' ? newValue : row.tl_ck;
    const thue_suat = field === 'ma_thue_data' ? newValue?.thue_suat || 0 : row.ma_thue_data?.thue_suat || 0;
    const gia_nt1 = field === 'ma_vt_data' ? newValue?.gia_ban : field === 'gia_nt1' ? newValue : row.gia_nt1;
    const tien_nt2 = (Number(so_luong) || 0) * (Number(gia_nt2) || 0);
    const ck_nt = (tien_nt2 * (Number(tl_ck) || 0)) / 100;
    const thue_nt = (tien_nt2 * (Number(thue_suat) || 0)) / 100;
    const t_tc = (Number(so_luong) || 0) * (Number(gia_nt2) || 0);
    return {
      tien_nt2,
      ck_nt,
      gia_nt1,
      thue_nt,
      t_tc
    };
  }
  return {};
};

import { formatMoney, formatNumber } from '@/lib/formatUtils';
import { FormField } from '@/components/custom/arito';
import { Label } from '@/components/ui/label';
import { FormFieldState } from '../../hooks';

interface BottomBarProps {
  totals: {
    t_so_luong: number;
    t_tien_nt2: number;
    t_thue_nt: number;
    t_km_nt: number;
    t_tc_tien_nt2: number;
    t_ck_nt: number;
    t_tt_nt: number;
  };
  state: FormFieldState;
}

export function BottomBar({ totals, state }: BottomBarProps) {
  return (
    <div className='w-full border-t bg-white px-4 py-2'>
      <div className='flex'>
        <div className='flex flex-col'>
          <div className='flex items-center'>
            <Label className='w-28 font-medium'>Tổng số lượng</Label>
            <div className='w-[100px] pb-1'>
              <FormField name='t_so_luong' type='text' disabled value={formatMoney(totals.t_so_luong)} />
            </div>
          </div>
        </div>

        <div className='ml-4 flex flex-col'>
          <div className='flex items-center'>
            <Label className='w-24 font-medium'>Tổng tiền</Label>
            <FormField
              name='t_tien_nt2'
              type='text'
              labelClassName='mr-2 font-medium'
              inputClassName='text-right'
              value={formatNumber(totals.t_tien_nt2)}
            />
          </div>
          <div className='flex items-center'>
            <Label className='w-24 font-medium'>Tổng thuế</Label>
            <FormField
              name='t_thue_nt'
              type='text'
              labelClassName='mr-2 font-medium'
              inputClassName='text-right'
              value={formatNumber(totals.t_thue_nt)}
            />
          </div>
        </div>

        <div className='ml-auto flex w-1/3 flex-col'>
          <div className='flex items-center'>
            <Label className='w-32 font-medium'>Tổng tiền KM</Label>
            <FormField
              name='t_km_nt'
              type='text'
              labelClassName='mr-2 font-medium'
              inputClassName='text-right'
              value={formatNumber(totals.t_km_nt)}
            />
          </div>
          <div className='flex items-center'>
            <Label className='w-32 font-medium'>Tổng chiết khấu</Label>
            <FormField
              name='t_ck_nt'
              type='text'
              labelClassName='mr-2 font-medium'
              inputClassName='text-right'
              value={formatNumber(totals.t_ck_nt)}
            />
          </div>
        </div>
        <div className='ml-auto flex w-1/3 flex-col'>
          <div className='flex items-center'>
            <Label className='w-32 font-medium'>Tổng tiền hàng</Label>
            <FormField
              name='t_tc_tien_nt2'
              type='text'
              labelClassName='mr-2 font-medium'
              inputClassName='text-right'
              value={formatNumber(totals.t_tc_tien_nt2)}
            />
          </div>
          <div className='flex items-center'>
            <Label className='w-32 font-medium'>Tổng thanh toán</Label>
            <FormField
              name='t_tt_nt'
              type='text'
              labelClassName='mr-2 font-medium'
              inputClassName='text-right'
              value={formatNumber(totals.t_tt_nt)}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
